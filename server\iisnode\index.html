<html>
<head>
    <title>iisnode logs</title>
    <style type="text/css">
        body
        {
            font-family: "Trebuchet MS" , Arial, Helvetica, sans-serif;
        }
        table
        {
            border-collapse: collapse;
        }
        td, th
        {
            border: 1px solid lightgray;
            padding: 3px 7px 2px 7px;
        }
        th
        {
            text-align: left;
            padding-top: 5px;
            padding-bottom: 4px;
            background-color: Gray;
            color: #ffffff;
        }
        td.stderr
        {
            color: Red;
        }
    </style>
</head>
<body>
    <table id="logFilesTable">
        <tr>
            <th>
                Computer name
            </th>
            <th>
                PID
            </th>
            <th>
                Type
            </th>
            <th>
                Created
            </th>
            <th>
                Link
            </th>
        </tr>
    </table>
    <p id="lastUpdated"></p>
    <script type="text/javascript">

        // this is replaced with actual data at runtime by code in interceptor.js
        var logFiles = [{"file":"LAPTOP-41PESEN1-13320-stderr-1753854605540.txt","computername":"LAPTOP-41PESEN1","pid":13320,"type":"stderr","created":1753854605540},{"file":"LAPTOP-41PESEN1-13320-stdout-1753854605538.txt","computername":"LAPTOP-41PESEN1","pid":13320,"type":"stdout","created":1753854605538},{"file":"LAPTOP-41PESEN1-22136-stderr-1753840365026.txt","computername":"LAPTOP-41PESEN1","pid":22136,"type":"stderr","created":1753840365026},{"file":"LAPTOP-41PESEN1-22136-stdout-1753840365023.txt","computername":"LAPTOP-41PESEN1","pid":22136,"type":"stdout","created":1753840365023},{"file":"LAPTOP-41PESEN1-22624-stderr-1753860901739.txt","computername":"LAPTOP-41PESEN1","pid":22624,"type":"stderr","created":1753860901739},{"file":"LAPTOP-41PESEN1-22624-stdout-1753860901737.txt","computername":"LAPTOP-41PESEN1","pid":22624,"type":"stdout","created":1753860901737},{"file":"LAPTOP-41PESEN1-22764-stderr-1753950907363.txt","computername":"LAPTOP-41PESEN1","pid":22764,"type":"stderr","created":1753950907363},{"file":"LAPTOP-41PESEN1-22764-stdout-1753950907354.txt","computername":"LAPTOP-41PESEN1","pid":22764,"type":"stdout","created":1753950907354},{"file":"LAPTOP-41PESEN1-24072-stderr-1753771851514.txt","computername":"LAPTOP-41PESEN1","pid":24072,"type":"stderr","created":1753771851514},{"file":"LAPTOP-41PESEN1-24072-stdout-1753771851512.txt","computername":"LAPTOP-41PESEN1","pid":24072,"type":"stdout","created":1753771851512},{"file":"LAPTOP-41PESEN1-25756-stderr-1753843291549.txt","computername":"LAPTOP-41PESEN1","pid":25756,"type":"stderr","created":1753843291549},{"file":"LAPTOP-41PESEN1-25756-stdout-1753843291547.txt","computername":"LAPTOP-41PESEN1","pid":25756,"type":"stdout","created":1753843291547},{"file":"LAPTOP-41PESEN1-26652-stderr-1753861442303.txt","computername":"LAPTOP-41PESEN1","pid":26652,"type":"stderr","created":1753861442303},{"file":"LAPTOP-41PESEN1-26652-stdout-1753861442301.txt","computername":"LAPTOP-41PESEN1","pid":26652,"type":"stdout","created":1753861442301},{"file":"LAPTOP-41PESEN1-30748-stderr-1753749415103.txt","computername":"LAPTOP-41PESEN1","pid":30748,"type":"stderr","created":1753749415103},{"file":"LAPTOP-41PESEN1-30748-stdout-1753756094599.txt","computername":"LAPTOP-41PESEN1","pid":30748,"type":"stdout","created":1753756094599},{"file":"LAPTOP-41PESEN1-30748-stdout-1753767596922.txt","computername":"LAPTOP-41PESEN1","pid":30748,"type":"stdout","created":1753767596922},{"file":"LAPTOP-41PESEN1-33716-stderr-1753949420617.txt","computername":"LAPTOP-41PESEN1","pid":33716,"type":"stderr","created":1753949420617},{"file":"LAPTOP-41PESEN1-33716-stdout-1753949420615.txt","computername":"LAPTOP-41PESEN1","pid":33716,"type":"stdout","created":1753949420615},{"file":"LAPTOP-41PESEN1-4972-stderr-1753771749763.txt","computername":"LAPTOP-41PESEN1","pid":4972,"type":"stderr","created":1753771749763},{"file":"LAPTOP-41PESEN1-4972-stdout-1753771749762.txt","computername":"LAPTOP-41PESEN1","pid":4972,"type":"stdout","created":1753771749762},{"file":"LAPTOP-41PESEN1-7584-stdout-1753771490013.txt","computername":"LAPTOP-41PESEN1","pid":7584,"type":"stdout","created":1753771490013}];
        var lastUpdated = 1753950914804;
        var date = new Date();

        date.setTime(lastUpdated);
        document.getElementById('lastUpdated').innerHTML = 'Index was last updated ' + date;

        logFiles.sort(function (a, b) {
            return b.created - a.created;
        });

        var logFilesTable = document.getElementById("logFilesTable");
        for (var i = 0; i < logFiles.length; i++) {
            var logFile = logFiles[i];
            date.setTime(logFile.created);
            var row = logFilesTable.insertRow(-1);
            var computerNameCell = row.insertCell(0);
            var pidCell = row.insertCell(1);
            var typeCell = row.insertCell(2);
            var dateCell = row.insertCell(3);
            var logCell = row.insertCell(4);
            computerNameCell.innerHTML = logFile.computername;
            pidCell.innerHTML = logFile.pid.toString();
            typeCell.innerHTML = logFile.type;
            typeCell.setAttribute('class', logFile.type);
            dateCell.innerHTML = date.toString();
            logCell.innerHTML = '<a href="' + logFile.file + '">log</a>';
        };

    </script>
</body>
</html>
